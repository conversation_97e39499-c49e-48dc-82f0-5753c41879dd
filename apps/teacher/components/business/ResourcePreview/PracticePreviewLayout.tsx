import { AssignCard } from "@/components/assign/assign-card";
import { AssignPageContainer } from "@/components/assign/assign-page-container";
import { PracticeGroupList } from "@/components/assign/assign-practice-preview/practice-group-list";
import { PracticeGroups } from "@/components/assign/assign-practice-preview/practice-groups";
import { ScrollArea } from "@/ui/scroll-area";
import usePracticePreviewModel from "./hooks/usePracticePreviewModel";
import usePracticeReportModel from "./hooks/usePracticeReportModel";

import { PageHeader } from "@/components/PageHeader";
import { CourseBaseInfo } from "@/types/assign/course";

export default function PracticePreviewLayout({
  id,
  onBack,
  headerSuffixNode,
  subjectKey,
  treeNodeInfo,
}: {
  id: string;
  onBack?: () => void;
  headerSuffixNode?: React.ReactNode;
  subjectKey: number;
  treeNodeInfo?: CourseBaseInfo;
}) {
  const { activeGroup, handleActiveGroupChange, title, practiceGroupList } =
    usePracticePreviewModel(id, treeNodeInfo);

  const { reportWidget } = usePracticeReportModel(subjectKey, treeNodeInfo);

  return (
    <AssignPageContainer>
      <div className="h-17.5 flex items-center justify-between pr-6">
        <PageHeader
          className={`h-17.5 flex items-center gap-3 truncate`}
          onBack={onBack}
          needBack={!!onBack}
        >
          <h1 className="text-gray-1 truncate whitespace-nowrap text-xl font-medium leading-[normal] tracking-wider">
            {title}
          </h1>
        </PageHeader>
        {headerSuffixNode}
      </div>
      <div className="flex flex-1 items-start gap-2.5 overflow-hidden pb-4 pl-6 pr-4">
        <AssignCard className="h-full flex-1 overflow-hidden">
          <ScrollArea orientation="vertical" className="h-full">
            <PracticeGroups
              practiceGroupList={practiceGroupList}
              activeGroup={activeGroup}
              onReport={reportWidget}
            />
          </ScrollArea>
        </AssignCard>
        <AssignCard className="w-66.5 h-full">
          <ScrollArea orientation="vertical" className="h-full">
            <PracticeGroupList
              practiceGroupList={practiceGroupList}
              activeGroup={activeGroup.value}
              onChange={handleActiveGroupChange}
            />
          </ScrollArea>
        </AssignCard>
      </div>
    </AssignPageContainer>
  );
}
