import { Card, CardContent } from "@/ui/card";
import { cn } from "@/utils/utils";
import { AssignHeading } from "./assign-heading";

interface AssignCardProps {
  style?: React.CSSProperties;
  className?: string;
  children?: React.ReactNode;
  title?: string;
  onScroll?: (e: React.UIEvent<HTMLDivElement>) => void;
}

export function AssignCard({
  style = {},
  className = "",
  children,
  title = "",
  onScroll,
}: AssignCardProps) {
  return (
    <Card
      className={cn(
        "rounded-2xl border-0 bg-white p-6 shadow-none outline-[1px] outline-offset-[-1px] outline-slate-200",
        className
      )}
      style={style}
      onScroll={onScroll}
    >
      {title && <AssignHeading content={title} />}
      <CardContent className="p-0 h-full">{children}</CardContent>
    </Card>
  );
}
