{"name": "teacher", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3030", "start": "next start --port 3030", "build": "next build", "build:dev": "NODE_ENV=dev next build", "build:test": "NODE_ENV=test next build && node scripts/static-upload.js", "build:test2": "NODE_ENV=test next build && node scripts/static-upload.js", "build:test3": "NODE_ENV=test next build && node scripts/static-upload.js", "build:prod": "NODE_ENV=production next build && node scripts/static-upload.js", "lint": "next lint", "build:local": "NODE_ENV=local next build", "deploy:dev": "NODE_ENV=production next build && node scripts/static-upload.js && { pm2 del schroolroom-tch || true; pm2 start -n schroolroom-tch pnpm -- run start; }"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@hookform/resolvers": "^5.2.1", "@preact-signals/safe-react": "^0.9.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@repo/core": "workspace:*", "@repo/ui": "workspace:*", "@sentry/nextjs": "^10.5.0", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.12", "@types/marked": "^6.0.0", "@types/react-select": "^5.0.1", "@uppy/aws-s3": "^4.3.2", "@uppy/core": "^4.5.2", "@uppy/dashboard": "^4.4.3", "@uppy/drag-drop": "^4.2.2", "@uppy/file-input": "^4.2.2", "@uppy/progress-bar": "^4.3.2", "@uppy/react": "^4.5.2", "@uppy/xhr-upload": "^4.4.2", "@use-gesture/react": "^10.3.1", "@xyflow/react": "^12.8.2", "ahooks": "^3.9.0", "antd": "^5.27.0", "antd-mobile": "^5.39.0", "await-to-js": "^3.0.0", "axios": "^1.11.0", "bowser": "^2.12.0", "bytes": "^3.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "colord": "^2.9.3", "copy-text-to-clipboard": "^3.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "file-type": "^21.0.0", "framer-motion": "^12.23.12", "html-to-image": "^1.11.13", "immer": "^10.1.1", "katex": "^0.16.22", "lottie-react": "^2.4.1", "lucide-react": "^0.475.0", "magic-bytes.js": "^1.12.1", "marked": "^15.0.12", "mime": "^4.0.7", "mitt": "^3.0.1", "next": "15.3.1", "ramda": "^0.30.1", "react": "catalog:react19", "react-day-picker": "8.10.1", "react-dom": "catalog:react19", "react-hook-form": "^7.62.0", "react-photo-view": "^1.2.7", "react-select": "^5.10.2", "react-virtuoso": "^4.14.0", "react-zoom-pan-pinch": "^3.7.0", "sanitize-html": "^2.17.0", "scheduler": "^0.26.0", "store2": "^2.14.4", "tailwind-merge": "^3.3.1", "ts-pattern": "^5.8.0", "tt-uploader": "^1.5.1", "zod": "^4.1.1"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4.1.11", "@types/bytes": "^3.1.5", "@types/node": "^20.19.10", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "@types/sanitize-html": "^2.16.0", "@types/scheduler": "^0.26.0", "ali-oss": "^6.23.0", "babel-plugin-react-compiler": "19.1.0-rc.2", "eslint-config-next": "15.3.1", "sharp": "^0.34.3", "tailwind-motion": "^0.0.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5.9.2", "vconsole": "^3.15.1"}}