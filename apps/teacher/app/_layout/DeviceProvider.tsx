"use client";
import { ACCESS_CHANNEL } from "@/enums";
import { DeviceContext } from "@/hooks";
import { sensorsManager } from "@/libs";
import {
  getAppInfo,
  getDeviceInfo,
  getScreenSize,
} from "@repo/lib/utils/device";
import { useMount } from "ahooks";
import dynamic from "next/dynamic";
import React from "react";
import { match } from "ts-pattern";
import "./init-app";

const AppSkeleton = dynamic(
  () => import(/*webpackPrefetch: true*/ "./AppSkeleton"),
  { ssr: false }
);

function DeviceProvider({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  /**
   * 只有 APP 端有值，设备尺寸
   */
  const screenSize = getScreenSize();
  /**
   * 访问渠道
   */
  const accessChannel = screenSize
    ? ACCESS_CHANNEL.Android
    : ACCESS_CHANNEL.Web;

  useMount(() => {
    const deviceInfo = getDeviceInfo();
    const appInfo = getAppInfo();

    const env = match<string, NodeJS.ProcessEnv["NODE_ENV"]>(
      window.location.origin
    )
      .when(
        (v) => v.includes("https://teacher.xiaoluxue.com"),
        () => "production"
      )
      .when(
        (v) => /https:\/\/teacher\.test\d?\.xiaoluxue\.cn/.test(v),
        () => "test"
      )
      .otherwise(() => "development");

    sensorsManager.init({
      platform: accessChannel,
      app_version: appInfo?.versionCode,
      device_id: deviceInfo?.deviceId,
      env,
    });
  });

  return (
    <>
      <AppSkeleton />

      <DeviceContext value={{ screenSize, accessChannel }}>
        {children}
      </DeviceContext>
    </>
  );
}

export default DeviceProvider;
