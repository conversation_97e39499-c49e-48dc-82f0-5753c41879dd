import {
  ContextMenu,
  ContextMenuItem,
} from "@/app/components/common/contextmenu";
import { RightSidebar } from "@/app/components/common/right-sidebar";
import { toast } from "@/app/components/common/toast";
import {
  DialogProps,
  DialogView,
} from "@/app/components/dialog/default-dialog";
import { GuideComment } from "@/app/components/guide/guide-comment";
import { GuideComments } from "@/app/components/guide/guide-comments";
import {
  useAddComment,
  useDeleteComment,
  useLikeComment,
  useReplyComment,
} from "@/app/models";
import {
  useCanSendComment,
  useSnitch,
  useSnitchTypes,
} from "@/app/models/comments/comments-model";
import { useClientContext } from "@/app/providers/client-provider";
import { withResolvers } from "@/app/utils/withResolvers";
import CommentIcon from "@/public/icons/ic_tab_growth_default.svg";
import {
  useComputed,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { useEffect } from "@preact-signals/safe-react/react";
import { Reference } from "@repo/core/types/data/comment";
import Button from "@repo/ui/components/press-button";
import { FC } from "react";
import { useCourseViewContext } from "../course/course-view-context";
import { useGuideViewContext } from "./guide-view-context";

const contextMenus: ContextMenuItem[] = [
  {
    icon: <CommentIcon className="size-[1.8em]" />,
    name: "评论",
  },
];
// 1=本班, 2=年级, 3=本校, 4=公开
const scopes = [
  {
    scope: "公开",
    value: 4,
  },
  {
    scope: "本校可见",
    value: 3,
  },
  {
    scope: "年级可见",
    value: 2,
  },
  {
    scope: "本班可见",
    value: 1,
  },
];

const objType = 100; // 100是课程

const SnitchContent: FC<{
  onSnitch: (res: { reportType: number; reportReason: string }) => void;
}> = ({ onSnitch }) => {
  const selectedSnitchTags = useSignal<number[]>([]);
  const reason = useSignal("");
  const { data } = useSnitchTypes();

  useSignalEffect(() => {
    if (!selectedSnitchTags.value.includes(7)) {
      reason.value = "";
    }
  });

  return (
    <div className="w-full text-start">
      <div className="text-text-1 mb-3 text-[15px] font-medium">
        请选择举报类型 <span className="text-dim-red">*</span>
      </div>
      <div className="min-w-87 grid grid-cols-3 gap-3">
        {data?.map((tag) => (
          <button
            key={tag.type}
            className={`rounded-lg text-[13px] leading-8 transition-colors ${
              selectedSnitchTags.value.includes(tag.type)
                ? "border border-[#FFBC21] bg-[#FFF0CD] text-[#FF9317]"
                : "bg-gray-bg text-text-1 border border-transparent"
            }`}
            onClick={() => {
              selectedSnitchTags.value = [tag.type];
            }}
          >
            {tag.name}
          </button>
        ))}
      </div>
      {selectedSnitchTags.value.includes(7) && (
        <>
          <div className="text-text-1 mb-3 mt-6 text-[15px] font-medium">
            请填写举报原因 <span className="text-dim-red">*</span>
          </div>
          <div className="bg-gray-bg flex flex-col rounded-lg px-4 py-3">
            <textarea
              className="placeholder:text-text-4 text-text-1 w-full flex-1 resize-none text-[13px] leading-8"
              placeholder="请描述一下举报原因"
              value={reason.value}
              onChange={(e) => {
                if (e.target.value.trim().length > 100) return;
                reason.value = e.target.value;
              }}
            />
            <div className="text-text-5 flex flex-[0_0_auto] justify-end">
              {reason.value.trim().length}/{100}
            </div>
          </div>
        </>
      )}
      <Button
        color="red"
        className="mt-9 w-full"
        disabled={
          selectedSnitchTags.value[0] === 7
            ? reason.value.trim().length <= 1
            : !selectedSnitchTags.value[0]
        }
        onClick={() => {
          onSnitch({
            reportType: selectedSnitchTags.value[0]!,
            reportReason: reason.value.trim(),
          });
        }}
      >
        提交举报
      </Button>
    </div>
  );
};

export const GuideCommentView: FC = () => {
  const { appInfo } = useClientContext();

  const {
    subjectId,
    lessonName,
    knowledgeName,
    hadShownRedirectComment,
    redirectCommentId,
    redirectCommentRootId,
    redirectReferenceId,
    studySessionId,
    studyType,
  } = useCourseViewContext();
  const {
    commentsBarVisible,
    commentInputVisible,
    ranges,
    commentRef,
    contextMenuPos,
    lessonId,
    knowledgeId,
    currentWidget,
    refreshReferences,
    markedRanges,
    markedReference,
    referenceList,
  } = useGuideViewContext();

  const comment = useSignal("");
  const scope = useSignal(4);
  const dialogProps = useSignal<DialogProps>({});
  const reply = useSignal<{
    commentId: number;
    commentContent: string;
    p: ReturnType<typeof withResolvers>;
  } | null>(null);
  const replyRefP = useSignal<ReturnType<typeof withResolvers> | null>(null);
  const { addComment } = useAddComment();
  const { likeComment } = useLikeComment();
  const { replyComment } = useReplyComment();
  const { deleteComment } = useDeleteComment();
  const getCanSendComment = useCanSendComment();
  const { trigger: snitch } = useSnitch();

  const {
    value: { quote, referenceType, referenceImage, studyContent },
  } = useComputed(() => {
    let studyContent: string | undefined;
    let referenceType = 0;
    let referenceImage = "";
    const quote = markedRanges.value.reduce((str, cur) => {
      let content = "";
      const doms = Array.from(
        document.querySelectorAll<HTMLSpanElement | HTMLImageElement>(
          `[data-line-id="${cur.lineId}"]`
        )
      );
      studyContent = studyContent || doms[0]?.dataset.rootTitle;
      if (cur.type === "pic") {
        referenceType |= 2;
        referenceImage =
          doms.find((dom) => dom.dataset?.type === "pic")?.dataset?.content ??
          "";
        content = "[图片]";
      } else {
        referenceType |= 1;
        content = doms
          .map((dom) =>
            Boolean(dom.dataset?.charId) &&
            dom.dataset.textureId === cur.textureId &&
            Number(dom.dataset.charId) >= Number(cur.start) &&
            Number(dom.dataset.charId) <= Number(cur.end)
              ? dom.dataset.content
              : ""
          )
          .join("");
      }
      return `${str}${content}`;
    }, "");
    return { quote, referenceType, referenceImage, studyContent };
  });

  const showContextMenu = ranges.length > 0;

  const handleSubmit = async () => {
    if (!currentWidget) return;
    const referenceId = Object.keys(
      markedReference.value?.referenceIds ?? {}
    )[0];
    const referencePositionMd5 = referenceId
      ? markedReference.value?.referenceIds[referenceId]
      : undefined;
    toast.show("发布中");
    if (reply.value) {
      const { commentId, p } = reply.value;
      const result = await replyComment({
        commentId,
        commentContent: comment.value,
        studySessionId,
        studyType,
        lessonId,
        widgetIndex: currentWidget.index,
        commentScope: scope.value,
      }).catch((e) => {
        if (/\[400\]/.test(e.message)) toast.error("内容违规，请重新编辑");
        return Promise.reject(e);
      });
      p.resolve(result);
    } else {
      const result = await addComment({
        knowledgeId,
        appVersion: appInfo?.versionName ?? "",
        lessonId,
        objId: lessonId,
        objType,
        commentContent: comment.value,
        commentScope: scope.value,
        referenceType,
        referenceContent: quote,
        referenceImage,
        referencePosition: { data: markedRanges.value },
        widgetIndex: currentWidget.index,
        widgetType: currentWidget.type,
        widgetName: currentWidget.name,
        subjectId: subjectId,
        courseName: lessonName,
        knowledgeName: knowledgeName,
        referenceId: referenceId ? Number(referenceId) : undefined,
        referencePositionMd5,
        studyContent,
      }).catch((e) => {
        if (/\[400\]/.test(e.message)) toast.error("内容违规，请重新编辑");
        return Promise.reject(e);
      });
      if (referenceId) {
        replyRefP.value?.resolve(result);
      }
    }
    comment.value = "";
    refreshReferences();
  };

  const handleLike = (commentId: number) => {
    return likeComment({
      commentId,
      objType,
      objId: lessonId,
      studySessionId,
      studyType,
      lessonId,
    });
  };

  const handleReply = async (commentId: number, commentContent: string) => {
    const p = withResolvers();
    if (await getCanSendComment()) {
      reply.value = { commentId, commentContent, p };
      commentInputVisible.value = true;
    } else {
      p.reject();
      toast.show("评论太频繁了，休息一下！");
    }
    return p.promise;
  };

  const handleDelete = async (commentId: number) => {
    const { promise, reject, resolve } = withResolvers();
    dialogProps.value = {
      title: "确认删除该评论",
      open: true,
      onClose: reject,
      buttons: [
        {
          text: "取消",
          color: "white",
          onClick: reject,
        },
        {
          text: "确认",
          color: "red",
          onClick: () => resolve(true),
        },
      ],
    };
    try {
      await promise;
      await deleteComment({
        commentId,
      });
    } finally {
      dialogProps.value = {};
      refreshReferences();
    }
  };

  const handleSnitch = async (commentId: number) => {
    const { promise, reject, resolve } = withResolvers();
    dialogProps.value = {
      title: "举报评论",
      open: true,
      onClose: reject,
      children: <SnitchContent onSnitch={(e) => resolve(e)} />,
    };
    try {
      const e = (await promise) as {
        reportType: number;
        reportReason: string;
      };
      await snitch({
        reportReason: e.reportReason || undefined,
        reportType: e.reportType,
        commentId,
      }).catch((e) => {
        if (e.message) toast.error(e.message);
        throw e;
      });
    } finally {
      dialogProps.value = {};
    }
  };

  useEffect(() => {
    if (hadShownRedirectComment.value) return;
    if (redirectReferenceId) {
      const ref = referenceList?.find(
        (item) => item.referenceId === Number(redirectReferenceId)
      );
      if (!ref) return;
      const pos = ref?.referencePosition.data[0];
      const start = pos?.type === "text" ? Number(pos.start) : 0;
      const end = pos?.type === "text" ? Number(pos.end) : 0;
      markedReference.value = {
        start,
        end,
        commentCount: ref.commentCount ?? 0,
        isLast: false,
        referenceIds: {
          [ref.referenceId]: ref.referencePositionMd5,
        },
        referencePosition:
          (ref?.referencePosition
            .data as Reference["referencePosition"]["data"]) ?? [],
        referenceType: ref?.referenceType ?? 0,
        replyed: ref.replyed,
      };
    }
  }, [
    hadShownRedirectComment.value,
    markedReference,
    redirectReferenceId,
    referenceList,
  ]);

  return (
    <>
      {showContextMenu && (
        <ContextMenu
          menu={contextMenus}
          onItemClick={async (item) => {
            if (item.name !== "评论") return;
            if (await getCanSendComment()) {
              markedReference.value = null;
              markedRanges.value = ranges;
              commentInputVisible.value = true;
            } else {
              toast.show("评论太频繁了，休息一下！");
            }
          }}
          port={commentRef}
          pos={contextMenuPos}
        />
      )}
      {commentInputVisible.value && (
        <GuideComment
          onSubmit={handleSubmit}
          scopes={scopes}
          comment={comment}
          scope={scope}
          quote={reply.value ? reply.value.commentContent : quote}
          reply={Boolean(reply.value)}
          onClose={() => {
            commentInputVisible.value = false;
            markedRanges.value = [];
            reply.value?.p.reject();
            reply.value = null;
            replyRefP.value?.reject();
            replyRefP.value = null;
          }}
        />
      )}
      <RightSidebar
        visible={commentsBarVisible.value}
        onClose={() => {
          if (commentInputVisible.value || dialogProps.value.open) return;
          markedReference.value = null;
          hadShownRedirectComment.value = true;
        }}
      >
        <GuideComments
          hadShownRedirectComment={hadShownRedirectComment.value}
          onRedirectedAction={() => {
            hadShownRedirectComment.value = true;
          }}
          redirectCommentId={redirectCommentId}
          redirectCommentRootId={redirectCommentRootId}
          objId={lessonId}
          referenceId={Object.keys(
            markedReference.value?.referenceIds ?? {}
          ).join(",")}
          onInputClickAction={async () => {
            const p = withResolvers();
            if (await getCanSendComment()) {
              replyRefP.value = p;
              markedRanges.value =
                markedReference.value?.referencePosition ?? [];
              commentInputVisible.value = true;
            } else {
              p.reject();
              toast.show("评论太频繁了，休息一下！");
            }
            return p.promise;
          }}
          inputValue={comment.value}
          onLikeAction={handleLike}
          onReplyAction={handleReply}
          onDeleteAction={handleDelete}
          onSnitchAction={handleSnitch}
        />
      </RightSidebar>
      <DialogView {...dialogProps.value} />
    </>
  );
};
