"use client";
import {
  IconButton,
  TranslucentGlassButton,
} from "@/app/components/guide/guide-buttons";
import { useChatViewModel } from "@/app/viewmodels/chat/chat-viewmodel";
// import IconAsk from "@/public/icons/ask.svg";
import IconBack from "@/public/icons/back.svg";
import IconAsk from "@/public/icons/icon-ask.svg";
import IconList from "@/public/icons/list.svg";
import { FC, useCallback } from "react";
import { useCourseViewContext } from "../course/course-view-context";
import { useGuideViewContext } from "./guide-view-context";

export const GuideMenuView: FC<{
  chatViewModel: ReturnType<typeof useChatViewModel>;
}> = ({ chatViewModel }) => {
  const {
    isProgressBarOpen,
    exit,
    trackEventWithLessonId,
    showPlayerControls,
  } = useGuideViewContext();

  // const { currentQuestion } = useQuestionPreviewContext();
  // console.log("currentQuestion", currentQuestion.questionId);

  const handleOpenProgressBar = useCallback(() => {
    isProgressBarOpen.value = true;
    trackEventWithLessonId("lesson_progress_click");
  }, [isProgressBarOpen, trackEventWithLessonId]);

  const handleOpenChat = useCallback(() => {
    chatViewModel.handleOpen();
  }, [chatViewModel]);

  const { askData } = useCourseViewContext();

  if (!showPlayerControls.value) {
    return null;
  }

  return (
    <div className="z-62 pointer-events-none absolute top-8 flex h-11 w-full flex-row items-center justify-between px-8">
      <IconButton icon={<IconBack />} onClick={exit} />
      <div className="pointer-events-auto flex flex-row items-center gap-3">
        {askData?.isShow && (
          <TranslucentGlassButton
            icon={<IconAsk className="mr-0.5" />}
            className="inline-flex h-10 items-center justify-center rounded-lg bg-white text-[rgba(51,48,45,0.7)] opacity-100 shadow-[0px_4px_16px_0px_rgba(35,42,64,0.05)] outline-[0.50px] outline-offset-[-0.50px] outline-zinc-800/10"
            onClick={handleOpenChat}
          >
            问一问
          </TranslucentGlassButton>
        )}
        <IconButton icon={<IconList />} onClick={handleOpenProgressBar} />
      </div>
    </div>
  );
};
