import { Span, startInactiveSpan } from "@sentry/nextjs";

interface Response<T> {
  code: number;
  data: T;
  message: string;
}

class ErrorWithTraceIdAndHttpStatus extends Error {
  constructor(
    message: string,
    public traceId?: string | null,
    public httpCode = "没有httpStatus",
    public isFromCdn: string | null = null,
    public isFromCache: string | null = null,
    name = "请求错误"
  ) {
    super(message);
    this.name = name;
  }
}

function abortSignalAny(signals: AbortSignal[]) {
  const controller = new AbortController();

  const onAbort = (signal: AbortSignal) => {
    controller.abort(signal.reason);
  };

  for (const signal of signals) {
    if (signal.aborted) {
      controller.abort(signal.reason);
      break;
    }
    signal.addEventListener("abort", () => onAbort(signal), { once: true });
  }

  return controller.signal;
}

function createTimerAbort(timeout = 5000, signal?: AbortSignal | null) {
  const abortController = new AbortController();
  let msg: string | undefined;
  const timer = setTimeout(() => {
    msg = `请求超过${timeout / 1000}秒未响应`;
    abortController.abort();
  }, timeout);
  return {
    signal: signal
      ? abortSignalAny([abortController.signal, signal])
      : abortController.signal,
    cancel() {
      clearTimeout(timer);
    },
    getMessage() {
      return msg;
    },
  };
}

async function* streamIterator(stream: ReadableStream) {
  const decoder = new TextDecoderStream();
  // const splitter = new TransformStream({
  //   transform(chunk, controller) {
  //     for (let i = 0; i < chunk.length; i++) {
  //       controller.enqueue(chunk.slice(i, i + 1));
  //     }
  //   },
  // });
  const textStream = stream.pipeThrough(decoder);
  // .pipeThrough(splitter);
  const reader = textStream.getReader();
  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      yield value;
    }
  } finally {
    reader.releaseLock();
  }
}

const getUri = (url: string | URL | globalThis.Request) => {
  if (url instanceof URL) {
    url = url.href;
  } else if (url instanceof globalThis.Request) {
    url = url.url;
  }
  return url.split("?")[0];
};

const createSpan = (
  url: string | URL | globalThis.Request,
  init?: RequestInit
) => {
  const startTime = Date.now();
  const span = startInactiveSpan({
    name: `${init?.method ?? "GET"} ${getUri(url)}`,
    startTime,
    op: "api",
    forceTransaction: true,
  });
  span.setAttribute("api.url", getUri(url));
  return span;
};

const res2ErrText = (response: globalThis.Response) =>
  response.text().catch((e) => (e?.message as string) || "网络请求失败");

const commonFetch = async (
  url: string | URL | globalThis.Request,
  init?: RequestInit,
  timeOut = 5000,
  span?: Span
) => {
  const { getMessage, cancel, signal } = createTimerAbort(
    timeOut,
    init?.signal
  );
  async function retry(left = 3): Promise<globalThis.Response> {
    try {
      const startTime = Date.now();
      const result = await fetch(url, {
        ...init,
        signal,
        headers: {
          ...init?.headers,
        },
      });
      cancel();
      span?.setAttribute("metric.req_duration", Date.now() - startTime);
      return result;
    } catch (e) {
      if (e instanceof TypeError && e.message.includes("Failed to fetch")) {
        console.log("跨域或网络问题", {
          请求路径: url,
          剩余重试次数: left - 1,
        });
        if (left > 0) {
          return new Promise((resolve) =>
            setTimeout(() => resolve(retry(left - 1)), 500)
          );
        }
      }
      throw new ErrorWithTraceIdAndHttpStatus(
        getMessage() || (e as Error)?.message || "重试后请求失败",
        undefined,
        undefined,
        null,
        null,
        "跨域或连接失败"
      );
    }
  }

  return retry();
};

export const fetchStream = async (
  url: string | URL | globalThis.Request,
  init?: RequestInit,
  timeOut = 5000
) => {
  const span = createSpan(url, init);
  const startTime1 = Date.now();
  try {
    const response = await commonFetch(url, init, timeOut, span);
    const startTime2 = Date.now();
    const traceId = response.headers.get("trace_id");
    const contentType = response.headers.get("Content-Type");
    if (!response.ok) {
      let errMsg = "网络请求失败";
      if (response.status === 405) {
        errMsg = "操作太频繁啦，请休息一下";
      } else if (contentType?.includes("application/json")) {
        const clone = response.clone();
        errMsg = await response
          .json()
          .then((json) => {
            if (!json.code || !json.message) throw json;
            return `[${json.code}] ${json.message}`;
          })
          .catch(() => res2ErrText(clone));
      } else {
        errMsg = await res2ErrText(response);
      }
      throw new ErrorWithTraceIdAndHttpStatus(
        errMsg || "网络请求失败",
        traceId,
        String(response.status)
      );
    }
    if (!contentType?.includes("text/plain")) {
      if (contentType?.includes("application/json")) {
        const res = (await response.json()) as Response<any>;
        const { code, message } = res;
        throw new ErrorWithTraceIdAndHttpStatus(
          `[${code}] ${message}`,
          traceId,
          String(response.status)
        );
      }
      throw new ErrorWithTraceIdAndHttpStatus(
        "不支持的Content-Type",
        traceId,
        String(response.status)
      );
    }
    if (!response.body) {
      throw new ErrorWithTraceIdAndHttpStatus(
        "请求失败，没有Body",
        traceId,
        String(response.status)
      );
    }
    span.setStatus({ code: 1 });
    span.setAttribute("metric.res_duration", Date.now() - startTime2);
    return streamIterator(response.body);
  } catch (err) {
    span.setStatus({ code: 2 });
    throw err;
  } finally {
    span.setAttribute("metric.full_duration", Date.now() - startTime1);
    span.end();
  }
};

const fetcher = async <T>(
  url: string | URL | globalThis.Request,
  init?: RequestInit,
  timeOut = 5000
) => {
  const span = createSpan(url, init);
  const startTime1 = Date.now();
  try {
    const response = await commonFetch(url, init, timeOut, span);
    const startTime2 = Date.now();
    const traceId = response.headers.get("trace_id");
    const contentType = response.headers.get("Content-Type");
    if (!response.ok) {
      let errMsg = "网络请求失败";
      if (response.status === 405) {
        errMsg = "操作太频繁啦，请休息一下";
      } else if (contentType?.includes("application/json")) {
        const clone = response.clone();
        errMsg = await response
          .json()
          .then((json) => {
            if (!json.code || !json.message) throw json;
            return `[${json.code}] ${json.message}`;
          })
          .catch(() => res2ErrText(clone));
      } else {
        errMsg = await res2ErrText(response);
      }
      throw new ErrorWithTraceIdAndHttpStatus(
        errMsg || "网络请求失败",
        traceId,
        String(response.status)
      );
    }
    const res = (await response.json().catch((e) => {
      throw new ErrorWithTraceIdAndHttpStatus(
        e?.message || "解析响应JSON失败",
        traceId,
        String(response.status)
      );
    })) as Response<T>;
    const { code, message, data } = res;

    if (code !== 0) {
      throw new ErrorWithTraceIdAndHttpStatus(
        `[${code}] ${message}`,
        traceId,
        String(response.status)
      );
    }
    span.setStatus({ code: 1 });
    span.setAttribute("metric.res_duration", Date.now() - startTime2);
    return data as T;
  } catch (err) {
    span.setStatus({ code: 2 });
    throw err;
  } finally {
    span.setAttribute("metric.full_duration", Date.now() - startTime1);
    span.end();
  }
};

export const fetchFile = async <T>(url: string, timeOut = 5000) => {
  const span = createSpan(url);
  const startTime1 = Date.now();
  let isFromCdn: string | null = null;
  let isFromCache: string | null = null;
  let httpCode: string | undefined = undefined;
  try {
    const response = await commonFetch(url, undefined, timeOut, span);
    const startTime2 = Date.now();
    isFromCdn = response.headers.get("x-cdn");
    isFromCache = response.headers.get("X-Use-File-Cache");
    httpCode = String(response.status);
    if (!response.ok) {
      throw new ErrorWithTraceIdAndHttpStatus(
        await res2ErrText(response),
        undefined,
        httpCode,
        isFromCdn,
        isFromCache,
        "获取文件错误"
      );
    }
    const blob = await response.blob();
    const text = await blob.text();
    span.setStatus({ code: 1 });
    span.setAttribute("metric.res_duration", Date.now() - startTime2);
    return JSON.parse(text) as T;
  } catch (err) {
    span.setStatus({ code: 2 });
    if (err instanceof ErrorWithTraceIdAndHttpStatus) throw err;
    throw new ErrorWithTraceIdAndHttpStatus(
      (err as Error)?.message || "获取文件失败",
      undefined,
      httpCode,
      isFromCdn,
      isFromCache,
      "获取文件错误"
    );
  } finally {
    span.setAttribute("metric.full_duration", Date.now() - startTime1);
    span.end();
  }
};

/**
 * TODO: 移动到各个apps内实现
 */
export async function get<T>(
  url: string,
  { query }: { query?: Record<string, string> }
) {
  const params = query ? new URLSearchParams(query) : undefined;
  return await fetcher<T>(`${url}?${params?.toString()}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
}

export default fetcher;
